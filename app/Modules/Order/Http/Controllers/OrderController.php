<?php

declare(strict_types=1);

namespace App\Modules\Order\Http\Controllers;

use App\Models\Order;
use App\Modules\Order\Data\OrderData;
use App\Modules\Order\Queries\OrdersQuery;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Spatie\LaravelData\PaginatedDataCollection;

final class OrderController
{
    use AuthorizesRequests;

    public function index(Request $request): JsonResponse
    {
        $orders = OrdersQuery::for(
            Order::query()->byClinic($request->clinicId())
        )->jsonPaginate();

        $data = OrderData::collect(
            $orders,
            PaginatedDataCollection::class
        )->include(
            'itemsCount',
            'vendorsCount',
        );

        return response()->json($data);
    }

    public function show(Order $order): JsonResponse
    {
        $this->authorize('view', $order);

        $order->load([
            'suborders',
            'suborders.vendor',
            'suborders.externalOrders',
            'items',
            'items.product',
            'items.product.productOffers',
            'promotions.promotion',
            'clinic',
        ]);

        $data = OrderData::from($order)
            ->include(
                'vendorOrders',
                'promotions',
            );

        return response()->json($data);
    }
}
