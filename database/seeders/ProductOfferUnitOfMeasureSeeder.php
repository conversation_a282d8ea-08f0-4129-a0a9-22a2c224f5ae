<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\ProductOffer;
use Illuminate\Database\Seeder;

class ProductOfferUnitOfMeasureSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Common unit of measure values for veterinary products
        $unitOfMeasureOptions = [
            'each',
            'bottle',
            'box',
            'vial',
            'tube',
            'pack',
            'bag',
            'case',
            'ml',
            'mg',
            'g',
            'kg',
            'oz',
            'lb',
            'tablet',
            'capsule',
            'dose',
            'syringe',
            'ampule',
            'jar',
        ];

        // Get product offers that don't have unit_of_measure set
        $productOffers = ProductOffer::whereNull('unit_of_measure')
            ->orWhere('unit_of_measure', '')
            ->limit(100) // Limit to avoid overwhelming the database
            ->get();

        $this->command->info("Updating unit_of_measure for {$productOffers->count()} product offers...");

        foreach ($productOffers as $offer) {
            // Assign a random unit of measure
            $randomUnit = $unitOfMeasureOptions[array_rand($unitOfMeasureOptions)];
            
            $offer->update([
                'unit_of_measure' => $randomUnit
            ]);
        }

        $this->command->info('Product offer unit_of_measure fields updated successfully!');
    }
}
